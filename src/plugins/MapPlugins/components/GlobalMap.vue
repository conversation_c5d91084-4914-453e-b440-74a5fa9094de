<script lang="ts" setup>
import { useGlobalMap } from '../composables';
import { GeolocateControls, Mapbox } from 'vue3-maplibre-gl';
import 'vue3-maplibre-gl/dist/style.css';

const {
  MAP_OPTIONS,
  isMapLoaded,
  registerMap,
  loadMap,
  registerGeoInstance,
  setGeoInstance,
  handleErrorGPS,
  handleSuccessGPS,
} = useGlobalMap();
</script>
<template>
  <Mapbox
    debug
    :options="MAP_OPTIONS"
    @click="
      (e) => {
        console.log(e);
      }
    "
    @register="registerMap"
  >
    <template v-if="isMapLoaded">
      <GeolocateControls
        :options="{
          positionOptions: {
            enableHighAccuracy: true,
          },
          trackUserLocation: true,
          showAccuracyCircle: true,
        }"
        @geolocate="handleSuccessGPS"
        @error="handleErrorGPS"
        @trackuserlocationend="setGeoInstance"
        @trackuserlocationstart="setGeoInstance"
        @register="registerGeoInstance"
      />
    </template>
  </Mapbox>
</template>
